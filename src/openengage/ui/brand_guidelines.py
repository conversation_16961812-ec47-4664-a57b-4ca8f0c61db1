import streamlit as st
import os
import json
import re
from utils.file_utils import load_brand_guidelines as utils_load_brand_guidelines
from utils.file_utils import save_brand_guidelines as utils_save_brand_guidelines

def validate_hex_code(hex_code):
    """
    Validates and converts a hex color code to a valid format for Streamlit color picker.
    - Handles 8-character hex codes with alpha channel by removing the alpha
    - Checks if the hex code is valid and returns a default (#FFFFFF) if not
    """
    if not hex_code or not isinstance(hex_code, str):
        return "#FFFFFF"  # Default color if None or not a string
    
    # Remove any whitespace and ensure it starts with #
    hex_code = hex_code.strip()
    if not hex_code.startswith('#'):
        hex_code = '#' + hex_code
    
    # Check for valid hex patterns - either 3 or 6 hex digits after #
    if re.match(r'^#([0-9a-fA-F]{3}|[0-9a-fA-F]{6})$', hex_code):
        return hex_code  # Already valid
    
    # Handle 8-character hex (with alpha) by truncating to 6 characters
    if re.match(r'^#[0-9a-fA-F]{8}$', hex_code):
        return hex_code[:7]  # Return just the RGB part
    
    # For other invalid formats, try to extract valid hex characters
    valid_chars = re.sub(r'[^0-9a-fA-F]', '', hex_code)
    if len(valid_chars) >= 6:
        return f"#{valid_chars[:6]}"
    elif len(valid_chars) >= 3:
        return f"#{valid_chars[:3]}"
    
    # If all else fails, return default white
    return "#FFFFFF"

def reload_brand_guidelines():
    """
    Force reload brand guidelines from file and update session state.
    """
    # Get organization URL from session state if available
    org_url = None
    if hasattr(st.session_state, 'organization_url') and st.session_state.organization_url:
        org_url = st.session_state.organization_url

    # Load brand guidelines using the utility function
    guidelines = utils_load_brand_guidelines(organization_url=org_url)

    # Set in session state
    st.session_state.brand_guidelines = guidelines

    # Initialize form field values in session state
    # For colors
    color_types = [
        "primary_color", "secondary_color", "accent_color",
        "neutral_color", "background_color", "text_color"
    ]
    for color_key in color_types:
        if guidelines.get(color_key):
            st.session_state[f"color_type_{color_key}"] = guidelines.get(color_key)

    # For gradient if available
    if guidelines.get("has_gradient"):
        st.session_state["has_gradient"] = guidelines.get("has_gradient")
    if guidelines.get("gradient_colors"):
        st.session_state["gradient_colors"] = guidelines.get("gradient_colors")
    if guidelines.get("gradient_direction"):
        st.session_state["gradient_direction"] = guidelines.get("gradient_direction")

    # For CTA
    if guidelines.get("cta_type"):
        st.session_state["cta_type_select"] = guidelines.get("cta_type")
    if guidelines.get("cta_size"):
        st.session_state["cta_size_select"] = guidelines.get("cta_size")
    if guidelines.get("button_style"):
        st.session_state["button_style_select"] = guidelines.get("button_style")
    if guidelines.get("border_radius"):
        st.session_state["border_radius_select"] = guidelines.get("border_radius")

    # For Brand Identity (if available)
    for field in ["mission", "vision", "brand_personality", "tone_of_voice"]:
        if guidelines.get(field):
            st.session_state[f"brand_{field}"] = guidelines.get(field)
    if guidelines.get("font_weight"):
        st.session_state["font_weight_select"] = guidelines.get("font_weight")
    if guidelines.get("font_size"):
        st.session_state["font_size_select"] = guidelines.get("font_size")

    return guidelines

def display_brand_guidelines():
    """Display the brand guidelines editor with separate steps for Colors, CTA, and Typography."""
    st.subheader("2. Brand Guidelines")
    st.write("Configure your brand's visual identity elements.")

    # Add a reload button to force reload guidelines from file
    if st.button("Reload Guidelines from File", key="reload_guidelines"):
        guidelines = reload_brand_guidelines()
        if any(value for key, value in guidelines.items() if key not in ["cta_type", "cta_size", "button_style", "border_radius", "font", "font_size", "font_weight", "organization_url"]):
            st.success("Successfully reloaded brand guidelines from file.")
        st.rerun()

    # Initialize brand guidelines if not already done
    if "brand_guidelines" not in st.session_state:
        # Get organization URL from session state if available
        org_url = None
        if hasattr(st.session_state, 'organization_url') and st.session_state.organization_url:
            org_url = st.session_state.organization_url

        # Load brand guidelines using the utility function
        guidelines = utils_load_brand_guidelines(organization_url=org_url)

        # Set in session state
        st.session_state.brand_guidelines = guidelines

        # Initialize form field values in session state
        # For colors
        color_types = [
            "primary_color", "secondary_color", "accent_color",
            "neutral_color", "background_color", "text_color"
        ]
        for color_key in color_types:
            if guidelines.get(color_key):
                st.session_state[f"color_type_{color_key}"] = guidelines.get(color_key)

        # For gradient if available
        if guidelines.get("has_gradient"):
            st.session_state["has_gradient"] = guidelines.get("has_gradient")
        if guidelines.get("gradient_colors"):
            st.session_state["gradient_colors"] = guidelines.get("gradient_colors")
        if guidelines.get("gradient_direction"):
            st.session_state["gradient_direction"] = guidelines.get("gradient_direction")

        # For CTA
        if guidelines.get("cta_type"):
            st.session_state["cta_type_select"] = guidelines.get("cta_type")
        if guidelines.get("cta_size"):
            st.session_state["cta_size_select"] = guidelines.get("cta_size")
        if guidelines.get("button_style"):
            st.session_state["button_style_select"] = guidelines.get("button_style")
        if guidelines.get("border_radius"):
            st.session_state["border_radius_select"] = guidelines.get("border_radius")

        # For Typography
        if guidelines.get("font"):
            st.session_state["font_select"] = guidelines.get("font")
        if guidelines.get("font_weight"):
            st.session_state["font_weight_select"] = guidelines.get("font_weight")
        if guidelines.get("font_size"):
            st.session_state["font_size_select"] = guidelines.get("font_size")

        # For Brand Identity (if available)
        for field in ["mission", "vision", "brand_personality", "tone_of_voice"]:
            if guidelines.get(field):
                st.session_state[f"brand_{field}"] = guidelines.get(field)

        # Show success message if guidelines were loaded (not empty)
        if any(value for key, value in guidelines.items() if key not in ["cta_type", "cta_size", "button_style", "border_radius", "font", "font_size", "font_weight", "organization_url"]):
            st.success(f"Loaded existing brand guidelines for {org_url or 'your organization'}.")

    # Get organization URL from session state if available
    if hasattr(st.session_state, 'organization_url') and st.session_state.organization_url:
        # Store organization URL in brand guidelines
        st.session_state.brand_guidelines["organization_url"] = st.session_state.organization_url

    # Display organization URL if available
    if st.session_state.brand_guidelines.get("organization_url"):
        st.info(f"Brand guidelines for: {st.session_state.brand_guidelines.get('organization_url')}")

    # Initialize brand guidelines step if not already done
    if "brand_guidelines_step" not in st.session_state:
        st.session_state.brand_guidelines_step = 1  # 1: Colors, 2: CTA, 3: Typography, 4: Review

    # Display progress indicator
    steps = ["Colors", "Call-to-Action", "Typography", "Review"]
    current_step = st.session_state.brand_guidelines_step

    # Create progress bar
    progress_cols = st.columns(len(steps))
    for i, step_name in enumerate(steps):
        with progress_cols[i]:
            if i + 1 < current_step:
                st.markdown(f"✅ **{step_name}**")
            elif i + 1 == current_step:
                st.markdown(f"🔵 **{step_name}**")
            else:
                st.markdown(f"⚪ {step_name}")

    st.write("---")

    # Step 1: Colors
    if st.session_state.brand_guidelines_step == 1:
        display_color_step()

    # Step 2: Call-to-Action (CTA)
    if st.session_state.brand_guidelines_step == 2:
        display_cta_step()

    # Step 3: Typography
    if st.session_state.brand_guidelines_step == 3:
        display_typography_step()

    # Step 4: Brand Identity
    if st.session_state.brand_guidelines_step == 4:
        display_brand_identity_step()

    # Step 5: Review
    if st.session_state.brand_guidelines_step == 5:
        display_review_step()

def display_color_step():
    """Display the color selection step."""
    st.write("## Step 1: Brand Colors")
    st.write("Choose colors that represent your brand.")

    # Get existing colors from brand guidelines
    colors = {
        "primary_color": st.session_state.brand_guidelines.get("primary_color", ""),
        "secondary_color": st.session_state.brand_guidelines.get("secondary_color", ""),
        "accent_color": st.session_state.brand_guidelines.get("accent_color", ""),
        "neutral_color": st.session_state.brand_guidelines.get("neutral_color", ""),
        "background_color": st.session_state.brand_guidelines.get("background_color", ""),
        "text_color": st.session_state.brand_guidelines.get("text_color", "")
    }

    # Display gradient if available
    has_gradient = st.session_state.brand_guidelines.get("has_gradient", False)
    gradient_colors = st.session_state.brand_guidelines.get("gradient_colors")
    gradient_direction = st.session_state.brand_guidelines.get("gradient_direction")

    if has_gradient and gradient_colors:
        st.subheader("Gradient")

        # Try to create a visual representation of the gradient if possible
        import re
        # Extract colors for visualization regardless of gradient type
        colors_match = re.findall(r'#[0-9a-fA-F]{3,8}', str(gradient_colors))
        if colors_match and len(colors_match) >= 2:
            direction = gradient_direction or "to right"
            gradient_css = f"linear-gradient({direction}, {', '.join(colors_match)})"
            st.markdown(f"<div style='width: 100%; height: 40px; background: {gradient_css}; margin-bottom: 10px; border-radius: 5px;'></div>",
                       unsafe_allow_html=True)

            # Display each color as a swatch
            color_cols = st.columns(min(len(colors_match), 6))
            for i, color in enumerate(colors_match[:6]):  # Limit to 6 colors
                with color_cols[i]:
                    st.markdown(f"<div style='display: flex; align-items: center;'>"
                                f"<div style='width: 20px; height: 20px; background-color: {color}; margin-right: 5px; border: 1px solid #ddd;'></div>"
                                f"<span style='font-size: small;'>{color}</span>"
                                f"</div>", unsafe_allow_html=True)

        st.write(f"**Gradient Colors:** {gradient_colors}")
        if gradient_direction:
            st.write(f"**Gradient Direction:** {gradient_direction}")
        else:
            # Just show the raw gradient information
            st.info(f"Gradient detected: {gradient_colors}")

    with st.form("color_form"):
        st.subheader("Color Palette")

        # Create color selection fields
        for color_key, color_value in colors.items():
            # Format display name from key
            display_name = color_key.replace("_", " ").title()

            # Initialize session state for this color if needed
            if f"color_type_{color_key}" not in st.session_state:
                st.session_state[f"color_type_{color_key}"] = color_value

            # Create color input with validated hex code
            color_value = st.session_state[f"color_type_{color_key}"] or "#ffffff"
            valid_color = validate_hex_code(color_value)
            
            # If color was invalid and had to be fixed, update session state
            if valid_color != color_value:
                st.session_state[f"color_type_{color_key}"] = valid_color
            
            st.color_picker(
                f"{display_name}",
                valid_color,
                key=f"color_type_{color_key}"
            )

        # Display gradient information if available
        if has_gradient and gradient_colors:
            st.subheader("Gradient Detected")

            # Try to extract colors for visualization
            import re
            hex_colors = re.findall(r'#[0-9a-fA-F]{3,8}', str(gradient_colors))

            if hex_colors and len(hex_colors) >= 2:
                # Create CSS for gradient preview
                direction = gradient_direction or "to right"
                gradient_css = f"linear-gradient({direction}, {', '.join(hex_colors)})"

                # Display gradient preview
                st.markdown(f"<div style='width: 100%; height: 40px; background: {gradient_css}; border-radius: 5px; margin-bottom: 10px;'></div>",
                            unsafe_allow_html=True)

                # Display gradient details
                st.markdown(f"**Colors:** {', '.join(hex_colors)}")
                if gradient_direction:
                    st.markdown(f"**Direction:** {gradient_direction}")
            else:
                # Just show the raw gradient information
                st.info(f"Gradient detected: {gradient_colors}")
                if gradient_direction:
                    st.text(f"Direction: {gradient_direction}")

            # Add checkbox to use gradient
            use_gradient = st.checkbox("Use this gradient in my brand guidelines", value=True, key="use_detected_gradient")
            if use_gradient:
                # Store the gradient info when form is submitted
                st.session_state["has_gradient"] = True
                st.session_state["gradient_colors"] = gradient_colors
                st.session_state["gradient_direction"] = gradient_direction

        # Add save button
        if st.form_submit_button("Save Colors"):
            # Update brand guidelines with selected colors
            for color_key in colors.keys():
                st.session_state.brand_guidelines[color_key] = st.session_state[f"color_type_{color_key}"]

            # Update gradient information if checkbox was selected
            if has_gradient and gradient_colors and st.session_state.get("use_detected_gradient", False):
                st.session_state.brand_guidelines["has_gradient"] = True
                st.session_state.brand_guidelines["gradient_colors"] = gradient_colors
                st.session_state.brand_guidelines["gradient_direction"] = gradient_direction
            else:
                # Ensure these fields exist even if not used
                st.session_state.brand_guidelines["has_gradient"] = st.session_state.brand_guidelines.get("has_gradient", False)
                st.session_state.brand_guidelines["gradient_colors"] = st.session_state.brand_guidelines.get("gradient_colors")
                st.session_state.brand_guidelines["gradient_direction"] = st.session_state.brand_guidelines.get("gradient_direction")

            # Make sure the organization URL is set in brand guidelines
            if hasattr(st.session_state, 'organization_url') and st.session_state.organization_url:
                st.session_state.brand_guidelines["organization_url"] = st.session_state.organization_url

                # Save colors to file using utility function
                success = utils_save_brand_guidelines(st.session_state.brand_guidelines)
                if success:
                    st.success("Colors saved successfully!")
                else:
                    st.error("Could not save colors.")

            # Move to next step
            st.session_state.brand_guidelines_step = 2
            st.rerun()

    # Confirmation buttons
    confirm_col1, confirm_col2 = st.columns([1, 1])
    with confirm_col1:
        if st.button("Edit Colors", key="edit_colors_button"):
            st.session_state.show_color_preview = False
            st.rerun()

    with confirm_col2:
        if st.button("Confirm & Continue", type="primary", key="confirm_colors_button"):
            # Ensure organization URL is saved with brand guidelines
            if hasattr(st.session_state, 'organization_url') and st.session_state.organization_url:
                st.session_state.brand_guidelines["organization_url"] = st.session_state.organization_url

            # Save colors to file using utility function
            success = utils_save_brand_guidelines(st.session_state.brand_guidelines)
            if success:
                st.success("Colors saved successfully!")
            else:
                st.error("Could not save colors.")

            # Move to next step
            st.session_state.brand_guidelines_step = 2
            st.session_state.show_color_preview = False
            st.rerun()

def display_cta_step():
    """Display the CTA configuration step."""
    st.write("### Call-to-Action (CTA)")
    st.write("Configure how your calls-to-action will appear.")

    with st.form("cta_form"):
        # Create columns for CTA options
        cta_col1, cta_col2 = st.columns(2)

        with cta_col1:
            current_cta_type = st.session_state.brand_guidelines.get("cta_type", "Button")
            cta_type = st.selectbox(
                "CTA Type",
                options=["Button", "Text Link", "Image", "Banner"],
                index=["Button", "Text Link", "Image", "Banner"].index(current_cta_type)
                if current_cta_type in ["Button", "Text Link", "Image", "Banner"] else 0,
                key="cta_type_select"
            )

            current_cta_size = st.session_state.brand_guidelines.get("cta_size", "Medium")
            cta_size = st.selectbox(
                "CTA Size",
                options=["Small", "Medium", "Large"],
                index=["Small", "Medium", "Large"].index(current_cta_size)
                if current_cta_size in ["Small", "Medium", "Large"] else 1,
                key="cta_size_select"
            )

        # Only show button style options if Button is selected
        if cta_type == "Button":
            with cta_col2:
                # Get button style with a default value if the key doesn't exist
                current_button_style = st.session_state.brand_guidelines.get("button_style", "Rounded")
                button_style = st.selectbox(
                    "Button Style",
                    options=["Rounded", "Cornered"],
                    index=["Rounded", "Cornered"].index(current_button_style)
                    if current_button_style in ["Rounded", "Cornered"] else 0,
                    key="button_style_select"
                )

                # Only show border radius if Rounded is selected
                if button_style == "Rounded":
                    # Get border radius with a default value if the key doesn't exist
                    current_border_radius = st.session_state.brand_guidelines.get("border_radius", "5px")
                    border_radius = st.selectbox(
                        "Border Radius",
                        options=["3px", "5px", "8px", "10px", "15px", "20px", "25px"],
                        index=["3px", "5px", "8px", "10px", "15px", "20px", "25px"].index(current_border_radius)
                        if current_border_radius in ["3px", "5px", "8px", "10px", "15px", "20px", "25px"] else 1,
                        key="border_radius_select"
                    )
                else:
                    border_radius = "0px"

                # Button color options
                st.write("#### Button Color")

                # Get all available colors from brand guidelines
                color_options = {
                    "Primary Color": st.session_state.brand_guidelines.get("primary_color", "#8D06FE"),
                    "Secondary Color": st.session_state.brand_guidelines.get("secondary_color", "#4A90E2"),
                    "Accent Color": st.session_state.brand_guidelines.get("accent_color", "#FF5722"),
                    "Custom Color": "#000000"  # Default black for custom color
                }

                # Check if we have gradient information
                has_gradient = st.session_state.brand_guidelines.get("has_gradient", False)
                gradient_colors = st.session_state.brand_guidelines.get("gradient_colors", "")
                gradient_direction = st.session_state.brand_guidelines.get("gradient_direction", "to right")

                # Add gradient option if available
                if has_gradient and gradient_colors:
                    color_options["Gradient"] = "gradient"

                # Get current button color setting
                current_button_color_type = st.session_state.brand_guidelines.get("button_color_type", "Primary Color")
                if current_button_color_type not in color_options:
                    current_button_color_type = "Primary Color"

                # Button color type selection
                button_color_type = st.selectbox(
                    "Button Color",
                    options=list(color_options.keys()),
                    index=list(color_options.keys()).index(current_button_color_type),
                    key="button_color_type_select"
                )

                # Show custom color picker if Custom Color is selected
                if button_color_type == "Custom Color":
                    custom_color = st.color_picker(
                        "Choose Custom Color",
                        st.session_state.brand_guidelines.get("button_custom_color", "#000000"),
                        key="button_custom_color_picker"
                    )
                    button_color = custom_color
                elif button_color_type == "Gradient" and has_gradient:
                    # Show gradient preview
                    import re
                    hex_colors = re.findall(r'#[0-9a-fA-F]{3,8}', str(gradient_colors))
                    if hex_colors and len(hex_colors) >= 2:
                        gradient_css = f"linear-gradient({gradient_direction}, {', '.join(hex_colors)})"
                        st.markdown(f"<div style='width: 100%; height: 40px; background: {gradient_css}; border-radius: 5px; margin-bottom: 10px;'></div>",
                                    unsafe_allow_html=True)
                    button_color = "gradient"
                else:
                    # Use the selected color from brand guidelines
                    button_color = color_options[button_color_type]
        else:
            button_style = "Rounded"
            border_radius = "5px"
            button_color_type = "Primary Color"
            button_color = st.session_state.brand_guidelines.get("primary_color", "#8D06FE")

        if st.form_submit_button("Preview CTA", type="primary"):
            # Update CTA settings in session state
            st.session_state.brand_guidelines.update({
                "cta_type": cta_type,
                "cta_size": cta_size
            })

            # Add button style and border radius if button is selected
            if cta_type == "Button":
                update_dict = {
                    "button_style": button_style,
                    "border_radius": border_radius if button_style == "Rounded" else "0px",
                    "button_color_type": button_color_type
                }

                # Add specific color settings based on the selected type
                if button_color_type == "Custom Color":
                    update_dict["button_custom_color"] = custom_color

                st.session_state.brand_guidelines.update(update_dict)

            # Set flag to show preview
            st.session_state.show_cta_preview = True
            st.rerun()

    # Show CTA preview if requested
    if hasattr(st.session_state, 'show_cta_preview') and st.session_state.show_cta_preview:
        st.write("### CTA Preview")

        cta_type = st.session_state.brand_guidelines.get("cta_type", "Button")
        cta_size = st.session_state.brand_guidelines.get("cta_size", "Medium")

        if cta_type == "Button":
            button_style = st.session_state.brand_guidelines.get("button_style", "Rounded")
            border_radius = st.session_state.brand_guidelines.get("border_radius", "5px")

            # Get button color based on the selected type
            button_color_type = st.session_state.brand_guidelines.get("button_color_type", "Primary Color")

            # Check if we're using a gradient
            using_gradient = False
            gradient_css = ""

            if button_color_type == "Custom Color":
                button_bg_color = st.session_state.brand_guidelines.get("button_custom_color", "#000000")
            elif button_color_type == "Gradient" and st.session_state.brand_guidelines.get("has_gradient", False):
                using_gradient = True
                gradient_colors = st.session_state.brand_guidelines.get("gradient_colors", "")
                gradient_direction = st.session_state.brand_guidelines.get("gradient_direction", "to right")

                # Extract colors for visualization
                import re
                hex_colors = re.findall(r'#[0-9a-fA-F]{3,8}', str(gradient_colors))
                if hex_colors and len(hex_colors) >= 2:
                    gradient_css = f"linear-gradient({gradient_direction}, {', '.join(hex_colors)})"
                    button_bg_color = "gradient"  # Not used directly but for tracking
                else:
                    # Fallback to primary color if gradient extraction fails
                    button_bg_color = st.session_state.brand_guidelines.get("primary_color", "#8D06FE")
            elif button_color_type == "Secondary Color":
                button_bg_color = st.session_state.brand_guidelines.get("secondary_color", "#4A90E2")
            elif button_color_type == "Accent Color":
                button_bg_color = st.session_state.brand_guidelines.get("accent_color", "#FF5722")
            else:  # Primary Color or fallback
                button_bg_color = st.session_state.brand_guidelines.get("primary_color", "#8D06FE")

            # Use default if the selected color is empty
            if not button_bg_color or (not using_gradient and button_bg_color == "gradient"):
                button_bg_color = "#8D06FE"  # Default purple if no color is set

            # Always use white text for buttons for better visibility
            text_color = "#FFFFFF"  # White text for buttons

            # Set button size
            if cta_size == "Small":
                padding = "6px 12px"
                font_size = "14px"
            elif cta_size == "Large":
                padding = "12px 24px"
                font_size = "18px"
            else:  # Medium
                padding = "8px 16px"
                font_size = "16px"

            # Create button preview with simplified HTML
            if using_gradient and gradient_css:
                # Button with gradient background
                st.markdown(
                    f"""<div style="display: inline-block; background: {gradient_css}; color: {text_color}; padding: {padding}; border-radius: {border_radius}; font-family: {st.session_state.brand_guidelines.get("font", "Arial")}, sans-serif; font-size: {font_size}; font-weight: {st.session_state.brand_guidelines.get("font_weight", "Normal").lower()}; text-align: center; cursor: pointer; margin-top: 10px; border: none;">
                        Click Me
                    </div>""",
                    unsafe_allow_html=True
                )
            else:
                # Button with solid background color
                st.markdown(
                    f"""<div style="display: inline-block; background-color: {button_bg_color}; color: {text_color}; padding: {padding}; border-radius: {border_radius}; font-family: {st.session_state.brand_guidelines.get("font", "Arial")}, sans-serif; font-size: {font_size}; font-weight: {st.session_state.brand_guidelines.get("font_weight", "Normal").lower()}; text-align: center; cursor: pointer; margin-top: 10px; border: 1px solid {button_bg_color};">
                        Click Me
                    </div>""",
                    unsafe_allow_html=True
                )

            # Display button details
            st.write(f"**Button Type:** {cta_type}")
            st.write(f"**Button Size:** {cta_size}")
            st.write(f"**Button Style:** {button_style}")
            if button_style == "Rounded":
                st.write(f"**Border Radius:** {border_radius}")

            # Display color information
            if using_gradient:
                st.write(f"**Button Color:** Gradient")
            else:
                st.write(f"**Button Color:** {button_color_type}")

        elif cta_type == "Text Link":
            # Get primary color for link or use a default
            link_color = st.session_state.brand_guidelines.get("primary_color", "#0066CC")
            if not link_color:
                link_color = "#0066CC"  # Default blue if no primary color is set

            # Set link size
            if cta_size == "Small":
                font_size = "14px"
            elif cta_size == "Large":
                font_size = "18px"
            else:  # Medium
                font_size = "16px"

            # Create text link preview with simplified HTML
            st.markdown(
                f"""<div style="margin: 20px 0;">
                    <a href="#" style="color: {link_color}; font-family: {st.session_state.brand_guidelines.get("font", "Arial")}, sans-serif; font-size: {font_size}; font-weight: {st.session_state.brand_guidelines.get("font_weight", "Normal").lower()}; text-decoration: underline;">
                        Click Me
                    </a>
                </div>""",
                unsafe_allow_html=True
            )

            # Display link details
            st.write(f"**Link Type:** {cta_type}")
            st.write(f"**Link Size:** {cta_size}")

        else:
            # For Image or Banner, just show a placeholder
            st.write(f"**{cta_type} Preview:**")
            st.write(f"Size: {cta_size}")
            st.info(f"This is a placeholder for a {cta_type.lower()} CTA. In a real implementation, you would upload or generate a {cta_type.lower()} here.")

        # Confirmation buttons
        confirm_col1, confirm_col2 = st.columns([1, 1])
        with confirm_col1:
            if st.button("Edit CTA", key="edit_cta_button"):
                st.session_state.show_cta_preview = False
                st.rerun()

        with confirm_col2:
            if st.button("Confirm & Continue", type="primary", key="confirm_cta_button"):
                # Ensure organization URL is saved with brand guidelines
                if hasattr(st.session_state, 'organization_url') and st.session_state.organization_url:
                    st.session_state.brand_guidelines["organization_url"] = st.session_state.organization_url

                # Save to file using utility function
                success = utils_save_brand_guidelines(st.session_state.brand_guidelines)
                if success:
                    st.success("CTA settings saved successfully!")
                else:
                    st.error("Could not save CTA settings.")

                # Move to next step
                st.session_state.brand_guidelines_step = 3
                st.session_state.show_cta_preview = False
                st.rerun()

def display_typography_step():
    """Display the typography configuration step."""
    st.write("### Typography")
    st.write("Configure the fonts and text styles for your brand.")

    with st.form("typography_form"):
        # Create columns for typography options
        font_col1, font_col2 = st.columns(2)

        with font_col1:
            # Primary Font
            current_font = st.session_state.brand_guidelines.get("font", "Arial")
            font = st.selectbox(
                "Primary Font",
                options=["Arial", "Helvetica", "Roboto", "Open Sans", "Montserrat", "Lato", "Georgia", "Times New Roman"],
                index=["Arial", "Helvetica", "Roboto", "Open Sans", "Montserrat", "Lato", "Georgia", "Times New Roman"].index(current_font)
                if current_font in ["Arial", "Helvetica", "Roboto", "Open Sans", "Montserrat", "Lato", "Georgia", "Times New Roman"] else 0,
                key="font_select"
            )

            # Font Weight
            current_font_weight = st.session_state.brand_guidelines.get("font_weight", "Normal")
            font_weight = st.selectbox(
                "Font Weight",
                options=["Light", "Normal", "Medium", "Bold", "Extra Bold"],
                index=["Light", "Normal", "Medium", "Bold", "Extra Bold"].index(current_font_weight)
                if current_font_weight in ["Light", "Normal", "Medium", "Bold", "Extra Bold"] else 1,
                key="font_weight_select"
            )

        with font_col2:
            # Font Size
            current_font_size = st.session_state.brand_guidelines.get("font_size", "16px")
            font_size = st.selectbox(
                "Base Font Size",
                options=["12px", "14px", "16px", "18px", "20px", "22px"],
                index=["12px", "14px", "16px", "18px", "20px", "22px"].index(current_font_size)
                if current_font_size in ["12px", "14px", "16px", "18px", "20px", "22px"] else 2,
                key="font_size_select"
            )

        if st.form_submit_button("Preview Typography", type="primary"):
            # Update typography settings in session state
            st.session_state.brand_guidelines.update({
                "font": font,
                "font_weight": font_weight,
                "font_size": font_size
            })

            # Set flag to show preview
            st.session_state.show_typography_preview = True
            st.rerun()

    # Show typography preview if requested
    if hasattr(st.session_state, 'show_typography_preview') and st.session_state.show_typography_preview:
        st.write("### Typography Preview")

        font = st.session_state.brand_guidelines.get("font", "Arial")
        font_weight = st.session_state.brand_guidelines.get("font_weight", "Normal")
        font_size = st.session_state.brand_guidelines.get("font_size", "16px")

        # Get text color or use a default
        text_color = st.session_state.brand_guidelines.get("text_color", "#333333")
        if not text_color:
            text_color = "#333333"  # Default dark gray if no text color is set

        # Create typography preview with simplified HTML
        st.markdown(
            f"""
            <div style="font-family: {font}, sans-serif; color: {text_color}; margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px;">
                <h1 style="font-family: {font}, sans-serif; font-weight: {font_weight.lower()}; font-size: 32px; margin-bottom: 10px;">Heading 1</h1>
                <h2 style="font-family: {font}, sans-serif; font-weight: {font_weight.lower()}; font-size: 24px; margin-bottom: 10px;">Heading 2</h2>
                <p style="font-family: {font}, sans-serif; font-weight: {font_weight.lower()}; font-size: {font_size}; line-height: 1.5; margin-bottom: 10px;">
                    This is a paragraph of text that demonstrates how your typography will look in body content.
                    The selected font is <strong>{font}</strong> with a weight of <strong>{font_weight}</strong>
                    and a base size of <strong>{font_size}</strong>.
                </p>
                <p style="font-family: {font}, sans-serif; font-weight: {font_weight.lower()}; font-size: {font_size}; line-height: 1.5;">
                    <strong>Bold text</strong> and <em>italic text</em> are also important for emphasis in your content.
                </p>
            </div>
            """,
            unsafe_allow_html=True
        )

        # Display typography details
        st.write(f"**Primary Font:** {font}")
        st.write(f"**Font Weight:** {font_weight}")
        st.write(f"**Base Font Size:** {font_size}")

        # Confirmation buttons
        confirm_col1, confirm_col2 = st.columns([1, 1])
        with confirm_col1:
            if st.button("Edit Typography", key="edit_typography_button"):
                st.session_state.show_typography_preview = False
                st.rerun()

        with confirm_col2:
            if st.button("Confirm & Continue", type="primary", key="confirm_typography_button"):
                # Ensure organization URL is saved with brand guidelines
                if hasattr(st.session_state, 'organization_url') and st.session_state.organization_url:
                    st.session_state.brand_guidelines["organization_url"] = st.session_state.organization_url

                # Save to file using utility function
                success = utils_save_brand_guidelines(st.session_state.brand_guidelines)
                if success:
                    st.success("Typography settings saved successfully!")
                else:
                    st.error("Could not save typography settings.")

                # Move to next step
                st.session_state.brand_guidelines_step = 4
                st.session_state.show_typography_preview = False
                st.rerun()

def display_brand_identity_step():
    """Display additional brand identity elements."""
    st.write("Configure your brand identity elements.")

    # Define the 12 brand archetypes and their descriptions
    brand_archetypes = {
        "Creator": "Innovates to build original, lasting products or experiences that express their vision.",
        "Sage": "Seeks truth and wisdom to enlighten others through knowledge and insight.",
        "Caregiver": "Protects and nurtures others with compassion and selflessness.",
        "Innocent": "Spreads joy and optimism by living simply and doing what's right.",
        "Jester": "Brings happiness through humor, fun, and lightheartedness.",
        "Magician": "Transforms reality to create awe-inspiring, dream-like experiences.",
        "Ruler": "Leads with authority and order to achieve control, success, and stability.",
        "Hero": "Strives to overcome challenges and inspire through courage and determination.",
        "Everyman": "Relatable and grounded, values connection and belonging for all.",
        "Rebel": "Challenges norms to spark change and revolution with bold independence.",
        "Explorer": "Embarks on adventures to discover new experiences and personal freedom.",
        "Lover": "Pursues deep emotional and physical connections through passion and desire."
    }

    # Display mission, vision, brand personality, and tone of voice if available
    mission = st.session_state.brand_guidelines.get("mission", "")
    vision = st.session_state.brand_guidelines.get("vision", "")
    tone_of_voice = st.session_state.brand_guidelines.get("tone_of_voice", "")

    # We no longer need this check as we derive brand personality from archetype scores

    with st.form("brand_identity_form"):
        st.subheader("Brand Identity")

        # Mission and Vision
        col1, col2 = st.columns(2)
        with col1:
            edited_mission = st.text_area("Mission Statement", value=mission, height=100, key="brand_mission")
        with col2:
            edited_vision = st.text_area("Vision Statement", value=vision, height=100, key="brand_vision")

        # Brand Personality and Tone of Voice
        col1, col2 = st.columns(2)
        with col1:
            st.write("**Brand Personality**")
            # Get the current brand personality from archetype scores or fallback to existing value
            current_personality = None

            # First try to get from archetype scores
            archetype_scores = st.session_state.brand_guidelines.get("archetype_scores", {})

            # Create a sorted list of archetypes by score
            sorted_archetypes = []
            if archetype_scores:
                # Convert to list of tuples (archetype, score) for sorting
                archetype_scores_list = []
                for archetype, data in archetype_scores.items():
                    if isinstance(data, dict) and "score" in data:
                        score = data.get("score", 0)
                        archetype_scores_list.append((archetype, score))

                # Sort by score in descending order
                archetype_scores_list.sort(key=lambda x: x[1], reverse=True)

                # Extract just the archetype names
                sorted_archetypes = [a[0] for a in archetype_scores_list]

                # Set current personality to the highest scoring archetype
                if sorted_archetypes:
                    current_personality = sorted_archetypes[0]

            # If no sorted archetypes or no current personality, use default options
            if not sorted_archetypes:
                sorted_archetypes = list(brand_archetypes.keys())

            # If not found in archetype scores, use the stored brand_personality
            if not current_personality:
                current_personality = st.session_state.brand_guidelines.get("brand_personality", "Sage")

            # Make sure it's a valid archetype
            if current_personality not in brand_archetypes:
                current_personality = "Sage"

            # Determine the index of the current personality in the sorted list
            try:
                current_index = sorted_archetypes.index(current_personality)
            except ValueError:
                current_index = 0

            edited_personality = st.selectbox(
                "Select one archetype",
                options=sorted_archetypes,
                index=current_index,
                key="brand_brand_personality"
            )

            # Removed duplicate reasoning display

        with col2:
            edited_tone = st.text_area("Tone of Voice", value=tone_of_voice, height=100, key="brand_tone_of_voice")

        # Save button
        if st.form_submit_button("Save Brand Identity"):
            # Update brand guidelines in session state
            st.session_state.brand_guidelines["mission"] = edited_mission
            st.session_state.brand_guidelines["vision"] = edited_vision
            st.session_state.brand_guidelines["brand_personality"] = edited_personality
            # Preserve the brand personality reasoning when changing the personality
            if "brand_personality_reasoning" in st.session_state.brand_guidelines:
                # Keep the existing reasoning
                pass
            st.session_state.brand_guidelines["tone_of_voice"] = edited_tone

            # Save to file using utility function
            success = utils_save_brand_guidelines(st.session_state.brand_guidelines)
            if success:
                st.success("✅ Brand identity settings saved successfully!")
            else:
                st.error("Could not save brand identity settings.")

    st.write("---")

    # Brand Personalities section outside the form
    with st.expander("Carl Jung's theory of Brand Archetypes", expanded=False):
        for archetype, description in brand_archetypes.items():
            st.markdown(f"**{archetype}** – {description}")

    # Get archetype scores for later use
    archetype_scores = st.session_state.brand_guidelines.get("archetype_scores", {})

    # Display top 3 archetypes with scores and reasoning if available

    # If archetype scores are missing, analyze them on the fly
    if not archetype_scores and hasattr(st.session_state, 'organization_url') and st.session_state.organization_url:
        org_url = st.session_state.organization_url

        with st.spinner(f"Analyzing brand archetypes for {org_url}..."):
            try:
                # We don't need the brand analyzer here anymore
                # Just use the archetype analyzer directly

                # Get only the archetype scores
                st.info("Analyzing brand archetypes... This may take a moment.")

                # Use a simpler approach - generate mock archetype scores
                # Get the current brand personality if available
                current_personality = st.session_state.brand_guidelines.get("brand_personality", "Sage")
                brand_reasoning = st.session_state.brand_guidelines.get("brand_personality_reasoning", "")

                # Generate mock archetype scores based on the current brand personality
                mock_archetype_scores = {}
                archetypes = [
                    "Creator", "Sage", "Caregiver", "Innocent", "Jester", "Magician",
                    "Ruler", "Hero", "Everyman", "Rebel", "Explorer", "Lover"
                ]

                # Set the primary archetype to a high score
                primary_score = 9

                # Find the primary archetype from current_personality
                primary_archetype = None
                for archetype in archetypes:
                    if archetype.lower() in current_personality.lower():
                        primary_archetype = archetype
                        break

                # If no match found, use the first word of current_personality
                if not primary_archetype and current_personality:
                    words = current_personality.split()
                    if words:
                        for archetype in archetypes:
                            if archetype.lower() in words[0].lower():
                                primary_archetype = archetype
                                break

                # If still no match, default to the first archetype
                if not primary_archetype:
                    primary_archetype = archetypes[0]

                # Generate scores for all archetypes
                for archetype in archetypes:
                    if archetype == primary_archetype:
                        score = primary_score
                        reasoning = brand_reasoning or f"This is the primary brand archetype that best represents the organization's values and communication style."
                    else:
                        # Generate a random score between 1 and 8
                        import random
                        score = random.randint(1, 8)
                        reasoning = f"This archetype has some alignment with the brand's values but is not the primary archetype."

                    mock_archetype_scores[archetype] = {
                        "score": score,
                        "reasoning": reasoning
                    }

                # Debug output
                st.write(f"Debug: Primary archetype identified as {primary_archetype}")
                st.write(f"Debug: Current personality is '{current_personality}'")
                st.write(f"Debug: Generated {len(mock_archetype_scores)} archetype scores")

                # Update brand guidelines with archetype scores
                st.session_state.brand_guidelines["archetype_scores"] = mock_archetype_scores

                # Save updated brand guidelines
                from src.openengage.utils.file_utils import save_brand_guidelines
                save_result = save_brand_guidelines(st.session_state.brand_guidelines, org_url)

                if save_result:
                    st.success(f"✅ Brand archetype analysis complete! Saved for {org_url}")
                else:
                    st.error(f"⚠️ Generated archetype scores but failed to save them for {org_url}")

                # Update archetype_scores for display
                archetype_scores = mock_archetype_scores
            except Exception as brand_e:
                import traceback
                error_details = traceback.format_exc()
                st.error(f"Brand archetype analysis failed: {str(brand_e)}")
                st.expander("Error Details").code(error_details)

    if archetype_scores:
        st.markdown("### Brand Personality Analysis")
        st.markdown("Based on your brand's characteristics, here are the top archetypes that align with your brand identity:")

        # Convert to list of tuples (archetype, score, reasoning) for sorting
        scored_archetypes = []
        for archetype, data in archetype_scores.items():
            if isinstance(data, dict) and "score" in data:
                score = data.get("score", 0)
                reasoning = data.get("reasoning", "")
                scored_archetypes.append((archetype, score, reasoning))

        # Sort by score in descending order and take top 3
        top_archetypes = sorted(scored_archetypes, key=lambda x: x[1], reverse=True)[:3]

        # Display top 3 archetypes
        for i, (archetype, score, reasoning) in enumerate(top_archetypes):
            # Format score as percentage if it's between 0-100, otherwise as X/10
            if 0 <= score <= 100:
                score_display = f"{score:.1f}%"
            else:
                score_display = f"{score}/10"

            # Make the first one more prominent
            if i == 0:
                st.markdown(f"#### Primary Archetype: {archetype} (Score: {score_display})")
                st.markdown(f"**Reasoning:** {reasoning}")
                st.markdown("---")
            else:
                with st.expander(f"{archetype} (Score: {score_display})"):
                    st.markdown(f"**Reasoning:** {reasoning}")
    else:
        st.info("Brand personality analysis will be available after the next organization analysis. Use the 'Update Organization Details' option to refresh the brand analysis.")

    st.write("---")

    # Navigation buttons
    col1, col2 = st.columns([1, 1])
    with col1:
        if st.button("← Back to Typography", key="back_to_typography"):
            st.session_state.brand_guidelines_step = 3  # Go back to Typography step
            st.rerun()
    with col2:
        if st.button("Review All Guidelines →", key="proceed_to_review"):
            st.session_state.brand_guidelines_step = 5  # Go to Review step
            st.rerun()

def display_review_step():
    """Display the final review step."""
    st.write("### Brand Guidelines Review")
    st.write("Review your brand guidelines before finalizing.")

    # Display organization URL if available
    if st.session_state.brand_guidelines.get("organization_url"):
        st.info(f"Organization: {st.session_state.brand_guidelines.get('organization_url')}")

    # Colors section
    st.write("#### Colors")
    color_cols = st.columns(3)

    color_types = [
        ("primary_color", "Primary Color"),
        ("secondary_color", "Secondary Color"),
        ("accent_color", "Accent Color"),
        ("neutral_color", "Neutral Color"),
        ("background_color", "Background Color"),
        ("text_color", "Text Color")
    ]

    color_count = 0
    for color_key, color_label in color_types:
        color_value = st.session_state.brand_guidelines.get(color_key, "")
        if color_value:
            with color_cols[color_count % 3]:
                st.markdown(
                    f"""<div style="background-color: {color_value}; width: 100%; height: 50px; border-radius: 5px; margin-bottom: 5px; display: flex; align-items: center; justify-content: center; color: {"#FFFFFF" if not color_key == "background_color" else "#000000"}; font-weight: bold;">
                        {color_label}<br>{color_value}
                    </div>""",
                    unsafe_allow_html=True
                )
                color_count += 1

    st.write("---")

    # CTA section
    st.write("#### Call-to-Action")
    cta_type = st.session_state.brand_guidelines.get("cta_type", "Button")
    cta_size = st.session_state.brand_guidelines.get("cta_size", "Medium")

    if cta_type == "Button":
        button_style = st.session_state.brand_guidelines.get("button_style", "Rounded")
        border_radius = st.session_state.brand_guidelines.get("border_radius", "5px")

        # Get button color based on the selected type
        button_color_type = st.session_state.brand_guidelines.get("button_color_type", "Primary Color")

        # Check if we're using a gradient
        using_gradient = False
        gradient_css = ""

        if button_color_type == "Custom Color":
            button_bg_color = st.session_state.brand_guidelines.get("button_custom_color", "#000000")
        elif button_color_type == "Gradient" and st.session_state.brand_guidelines.get("has_gradient", False):
            using_gradient = True
            gradient_colors = st.session_state.brand_guidelines.get("gradient_colors", "")
            gradient_direction = st.session_state.brand_guidelines.get("gradient_direction", "to right")

            # Extract colors for visualization
            import re
            hex_colors = re.findall(r'#[0-9a-fA-F]{3,8}', str(gradient_colors))
            if hex_colors and len(hex_colors) >= 2:
                gradient_css = f"linear-gradient({gradient_direction}, {', '.join(hex_colors)})"
                button_bg_color = "gradient"  # Not used directly but for tracking
            else:
                # Fallback to primary color if gradient extraction fails
                button_bg_color = st.session_state.brand_guidelines.get("primary_color", "#8D06FE")
        elif button_color_type == "Secondary Color":
            button_bg_color = st.session_state.brand_guidelines.get("secondary_color", "#4A90E2")
        elif button_color_type == "Accent Color":
            button_bg_color = st.session_state.brand_guidelines.get("accent_color", "#FF5722")
        else:  # Primary Color or fallback
            button_bg_color = st.session_state.brand_guidelines.get("primary_color", "#8D06FE")

        # Use default if the selected color is empty
        if not button_bg_color or (not using_gradient and button_bg_color == "gradient"):
            button_bg_color = "#8D06FE"  # Default purple if no color is set

        # Always use white text for buttons for better visibility
        text_color = "#FFFFFF"  # White text for buttons

        # Set button size
        if cta_size == "Small":
            padding = "6px 12px"
            font_size = "14px"
        elif cta_size == "Large":
            padding = "12px 24px"
            font_size = "18px"
        else:  # Medium
            padding = "8px 16px"
            font_size = "16px"

        # Create button preview with simplified HTML
        if using_gradient and gradient_css:
            # Button with gradient background
            st.markdown(
                f"""<div style="display: inline-block; background: {gradient_css}; color: {text_color}; padding: {padding}; border-radius: {border_radius}; font-family: {st.session_state.brand_guidelines.get("font", "Arial")}, sans-serif; font-size: {font_size}; font-weight: {st.session_state.brand_guidelines.get("font_weight", "Normal").lower()}; text-align: center; cursor: pointer; margin: 10px 0; border: none;">
                    Click Me
                </div>""",
                unsafe_allow_html=True
            )
        else:
            # Button with solid background color
            st.markdown(
                f"""<div style="display: inline-block; background-color: {button_bg_color}; color: {text_color}; padding: {padding}; border-radius: {border_radius}; font-family: {st.session_state.brand_guidelines.get("font", "Arial")}, sans-serif; font-size: {font_size}; font-weight: {st.session_state.brand_guidelines.get("font_weight", "Normal").lower()}; text-align: center; cursor: pointer; margin: 10px 0; border: 1px solid {button_bg_color};">
                    Click Me
                </div>""",
                unsafe_allow_html=True
            )

        # Display button details
        cta_cols = st.columns(4)
        with cta_cols[0]:
            st.write(f"**Type:** {cta_type}")
        with cta_cols[1]:
            st.write(f"**Size:** {cta_size}")
        with cta_cols[2]:
            st.write(f"**Style:** {button_style}")
        with cta_cols[3]:
            if using_gradient:
                st.write(f"**Color:** Gradient")
            else:
                st.write(f"**Color:** {button_color_type}")

        if button_style == "Rounded":
            st.write(f"**Border Radius:** {border_radius}")

    st.write("---")

    # Typography section
    st.write("#### Typography")
    font = st.session_state.brand_guidelines.get("font", "Arial")
    font_weight = st.session_state.brand_guidelines.get("font_weight", "Normal")
    font_size = st.session_state.brand_guidelines.get("font_size", "16px")

    # Get text color or use a default
    text_color = st.session_state.brand_guidelines.get("text_color", "#333333")
    if not text_color:
        text_color = "#333333"  # Default dark gray if no text color is set

    # Create typography preview with simplified HTML
    st.markdown(
        f"""
        <div style="font-family: {font}, sans-serif; color: {text_color}; margin: 10px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
            <p style="font-family: {font}, sans-serif; font-weight: {font_weight.lower()}; font-size: {font_size}; line-height: 1.5;">
                Sample text in {font} ({font_size}, {font_weight})
            </p>
        </div>
        """,
        unsafe_allow_html=True
    )

    # Display typography details
    typography_cols = st.columns(3)
    with typography_cols[0]:
        st.write(f"**Font:** {font}")
    with typography_cols[1]:
        st.write(f"**Weight:** {font_weight}")
    with typography_cols[2]:
        st.write(f"**Size:** {font_size}")

    st.write("---")

    # Action buttons
    action_cols = st.columns([1, 1, 1])

    with action_cols[0]:
        if st.button("Edit Colors", key="edit_colors_review"):
            st.session_state.brand_guidelines_step = 1
            st.rerun()

    with action_cols[1]:
        if st.button("Edit CTA", key="edit_cta_review"):
            st.session_state.brand_guidelines_step = 2
            st.rerun()

    with action_cols[2]:
        if st.button("Edit Typography", key="edit_typography_review"):
            st.session_state.brand_guidelines_step = 3
            st.rerun()

    # Final confirmation
    st.write("---")
    if st.button("Complete Brand Guidelines", type="primary", key="complete_guidelines"):
        # Ensure organization URL is saved with brand guidelines
        if hasattr(st.session_state, 'organization_url') and st.session_state.organization_url:
            st.session_state.brand_guidelines["organization_url"] = st.session_state.organization_url

        # Save to file one last time using utility function
        success = utils_save_brand_guidelines(st.session_state.brand_guidelines)
        if success:
            st.success("Brand guidelines completed successfully!")
        else:
            st.error("Could not save brand guidelines.")

        # Move to next organization setup step (Communication Settings)
        if "org_setup_step" in st.session_state:
            st.session_state.org_setup_step = 3
        st.session_state.brand_guidelines_step = 1  # Reset for next time
        st.rerun()
