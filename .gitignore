auth
# Environment variables
.env
.env.*
!.env.example

# Python
*__pycache__/
*.py[cod]
*$py.class
*.so
.Python

# Virtual Environment
env/
venv/
myenv/
myenv/*
.venv/
ENV/
virtualenv/
pythonenv*/
.env/
.virtualenv/
venv/*
.venv/*
env.bak/
!env.bak/.gitkeep
venv.bak/
!venv.bak/.gitkeep
.venv.bak/
!.venv.bak/.gitkeep

# IDE
.idea/
.vscode/
*.swp
*.swo
*.swn
.DS_Store
Thumbs.db
*.sublime-project
*.sublime-workspace

# Project specific
config/*.json
**/data/campaigns/mass*
**/data/images/**
**/data/**/*.json
**/data/**/*.jsonl
**/data/**/*.html
**/data/*.json
**/data/*.jsonl
**/data/**/*.csv
**/data/**/*.pdf
**/Sample Data For Mass Generation/**.csv
**/Sample Data For Mass Generation/**.json
!data/.gitkee
!data/OEShortLogo.png
!data/Openengage logo.png
!data/brand colours
!data/default_domain_stages.json
popupdata.json
**/popup_background.jpg
email_marketing.db

# Logs
*.log
logs/
log/

# Docker
.docker/
*.env
.dockerignore

# Documentation
docs/_build/
site/

# Jupyter
.ipynb_checkpoints/
*.ipynb
Codes/.ipynb_checkpoints/
